# 半托找图WEB版

这是原Python桌面程序的Web版本，提供了完整的商品数据提取和图片下载功能。

## 功能特性

### 核心功能
- **SKU提取** - 从API提取商品SKU编号
- **图片下载** - 根据商品信息下载对应图片
- **SKU搜索** - 根据SKU搜索商品名称和图片
- **SKU对比** - 对比API中的SKU与本地文件
- **图片对比选择** - 左右对比API图片和本地图片，手动选择下载
- **文件重命名** - 批量重命名文件功能
- **Everything集成** - 与Everything搜索引擎集成

### 新增Web功能
- **直观的Web界面** - 替代原有的命令行界面
- **实时进度显示** - 任务执行进度可视化
- **操作日志** - 实时显示操作状态和结果
- **图片预览** - 在线预览和对比图片
- **配置管理** - Web界面配置所有参数

## 安装和运行

### 环境要求
- Python 3.7+
- Everything搜索引擎（用于本地文件搜索）

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行应用
```bash
python 主程序.py
```

应用将在 http://127.0.0.1:5000 启动

## 使用说明

### 1. 配置设置
在主页面配置以下信息：
- **API地址** - 商品数据API的完整URL
- **Cookie** - 用于API认证的Cookie信息
- **搜索路径** - 本地图片搜索的基础路径
- **共享文件夹** - 用于SKU对比的共享文件夹路径

### 2. 基本操作流程

#### 提取SKU
1. 配置API地址和Cookie
2. 点击"提取SKU"按钮
3. 系统自动从API提取SKU并填入数据输入区

#### 图片对比下载
1. 在商品数据输入区输入SKU（每行一个）
2. 点击"下载缺失SKU"按钮
3. 系统跳转到图片对比页面
4. 左侧显示API商品图，右侧显示本地搜索到的图片
5. 选择需要的图片进行下载

#### SKU对比
1. 配置API地址、Cookie和共享文件夹
2. 点击"SKU对比"按钮
3. 系统对比API中的SKU与共享文件夹中的文件
4. 显示缺失的SKU列表

### 3. 高级配置

#### 搜索选项
- **目标路径后缀** - 限制搜索特定路径后缀的文件
- **忽略文件名前后字符** - 在文件名匹配时忽略前后指定数量的字符
- **严格搜索路径限制** - 只在指定目录中搜索

#### Everything集成
确保Everything搜索引擎正在运行，并启用HTTP服务器功能：
1. 打开Everything
2. 工具 → 选项 → HTTP服务器
3. 启用HTTP服务器
4. 设置端口为8080（或在配置中修改）

## 文件结构

```
半托找图WEB版/
├── 主程序.py              # Flask主程序
├── requirements.txt       # Python依赖
├── settings.ini          # 配置文件（自动生成）
├── key.vdf              # 授权文件（自动生成）
├── templates/           # HTML模板
│   ├── 主页.html
│   ├── 图片对比.html
│   └── 错误页面.html
├── static/             # 静态资源
│   ├── css/
│   │   ├── 样式.css
│   │   └── 对比页面.css
│   ├── js/
│   │   ├── 主脚本.js
│   │   └── 图片对比.js
│   └── images/         # 图片资源
└── README.md           # 说明文档
```

## 注意事项

1. **授权验证** - 应用启动时会验证key.vdf文件，有效期30天
2. **网络要求** - 需要能够访问商品API和Everything HTTP服务
3. **文件权限** - 确保应用有读写本地文件的权限
4. **浏览器兼容** - 推荐使用Chrome、Firefox等现代浏览器

## 故障排除

### 常见问题
1. **授权文件错误** - 检查key.vdf文件是否存在且未过期
2. **API访问失败** - 检查网络连接和Cookie是否有效
3. **Everything搜索失败** - 确保Everything正在运行且HTTP服务已启用
4. **图片加载失败** - 检查图片URL是否可访问

### 日志查看
应用运行时会在控制台显示详细日志，Web界面也有操作日志区域显示实时状态。

## 技术支持

如有问题请检查：
1. Python版本是否符合要求
2. 所有依赖是否正确安装
3. 配置信息是否正确填写
4. Everything服务是否正常运行