# 图片对比功能使用说明

## 功能概述

图片对比页面是专门为图片选择和对比设计的全屏界面，去除了所有不必要的元素，专注于图片对比体验。

## 界面布局

### 全屏左右对比
- **左侧**：显示API商品图
- **右侧**：显示本地搜索到的图片
- **顶部信息栏**：显示当前SKU和进度信息
- **底部操作区**：提供跳过和返回按钮

### 图片显示特性
- 图片占满左右两个屏幕区域
- 支持鼠标悬停放大镜效果
- 图片自适应大小，保持比例
- 加载状态和错误状态清晰显示

## 核心功能

### 1. 鼠标悬停放大
- 将鼠标悬停在任意图片上
- 自动显示200px圆形放大镜
- 2倍放大效果，跟随鼠标移动
- 离开图片区域自动隐藏

### 2. 多图片选择
- 当本地搜索到多张图片时
- 底部显示缩略图选择器
- 点击缩略图切换主显示图片
- 当前选中图片高亮显示

### 3. 图片下载选择
- 左侧API图片：点击"选择API图片"按钮
- 右侧本地图片：点击"选择本地图片"按钮
- 弹出确认对话框
- 确认后自动下载并处理下一个SKU

## 操作流程

### 启动对比
1. 在主页面输入SKU数据
2. 点击"下载缺失SKU"按钮
3. 自动跳转到图片对比页面

### 对比选择
1. 查看左右两侧图片
2. 使用鼠标悬停查看细节
3. 如有多张本地图片，点击底部缩略图切换
4. 选择要下载的图片（API或本地）
5. 确认下载

### 批量处理
- 自动处理所有SKU
- 显示实时进度
- 可随时跳过当前SKU
- 完成后显示处理结果摘要

## 界面优化

### 视觉设计
- 黑色背景，突出图片内容
- 半透明控件，不干扰图片查看
- 平滑动画过渡
- 现代化按钮和图标

### 响应式适配
- 移动端自动切换为上下布局
- 缩略图和按钮大小自适应
- 放大镜效果在小屏幕上调整

### 性能优化
- 图片懒加载
- 平滑的加载动画
- 高效的DOM操作
- 内存管理优化

## 快捷操作

- **鼠标悬停**：查看图片细节
- **点击缩略图**：切换本地图片
- **ESC键**：关闭确认对话框
- **空格键**：跳过当前SKU（计划中）

## 注意事项

1. **网络要求**：需要稳定的网络连接加载API图片
2. **Everything服务**：确保Everything HTTP服务正常运行
3. **浏览器兼容**：推荐使用Chrome、Firefox等现代浏览器
4. **图片格式**：支持常见的图片格式（JPG、PNG、GIF等）

## 故障排除

### 图片无法加载
- 检查网络连接
- 确认API图片URL有效
- 验证Everything服务状态

### 放大镜不工作
- 确保使用现代浏览器
- 检查JavaScript是否启用
- 刷新页面重试

### 缩略图不显示
- 检查本地图片路径
- 确认Everything搜索结果
- 验证图片文件权限