/* 半托找图WEB版 - 样式文件 */

/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "微软雅黑", "Microsoft YaHei", Arial, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #2d3748;
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
    padding: 30px 0;
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.header h1 {
    font-size: 2.5rem;
    color: #4299e1;
    margin-bottom: 10px;
    font-weight: bold;
}

.header .subtitle {
    font-size: 1.1rem;
    color: #4a5568;
    font-weight: normal;
}

/* 区域样式 */
section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

section h2 {
    font-size: 1.4rem;
    color: #2d3748;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e2e8f0;
    font-weight: bold;
}

section h2 i {
    margin-right: 10px;
    color: #4299e1;
}

/* 配置组样式 */
.config-group {
    margin-bottom: 25px;
    padding: 20px;
    background: #f7fafc;
    border-radius: 10px;
    border-left: 4px solid #4299e1;
}

.config-group h3 {
    font-size: 1.1rem;
    color: #2d3748;
    margin-bottom: 15px;
    font-weight: bold;
}

/* 表单样式 */
.form-row {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    gap: 10px;
    flex-wrap: wrap;
}

.form-row label {
    min-width: 120px;
    font-weight: 500;
    color: #4a5568;
    text-align: right;
}

.form-input {
    flex: 1;
    min-width: 200px;
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.small-input {
    width: 80px;
    padding: 8px 10px;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    text-align: center;
}

.form-textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    font-family: "微软雅黑", monospace;
    resize: vertical;
    min-height: 120px;
    transition: all 0.3s ease;
}

.form-textarea:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

/* 复选框样式 */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-weight: normal;
    min-width: auto;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #4299e1;
}

.inline-group {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-left: 15px;
}

.help-text {
    font-size: 0.9rem;
    color: #ed8936;
    font-style: italic;
    margin-top: 5px;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    min-width: 120px;
    justify-content: center;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn i {
    font-size: 16px;
}

.btn-primary {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
}

.btn-secondary {
    background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
}

.btn-purple {
    background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
    color: white;
}

/* 按钮网格 */
.button-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

/* SKU搜索组 */
.sku-search-group {
    margin-top: 20px;
    padding: 20px;
    background: #f7fafc;
    border-radius: 10px;
    border-left: 4px solid #9f7aea;
}

.sku-search-group h3 {
    font-size: 1.1rem;
    color: #2d3748;
    margin-bottom: 15px;
    font-weight: bold;
}

/* 控制台样式 */
.console {
    background: #1a202c;
    color: #e2e8f0;
    padding: 20px;
    border-radius: 8px;
    font-family: "Consolas", "Monaco", monospace;
    font-size: 13px;
    line-height: 1.5;
    max-height: 400px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.console::-webkit-scrollbar {
    width: 8px;
}

.console::-webkit-scrollbar-track {
    background: #2d3748;
    border-radius: 4px;
}

.console::-webkit-scrollbar-thumb {
    background: #4a5568;
    border-radius: 4px;
}

.console::-webkit-scrollbar-thumb:hover {
    background: #718096;
}

.console-controls {
    margin-top: 15px;
    text-align: right;
}

/* 日志颜色 */
.log-info {
    color: #e2e8f0;
}

.log-success {
    color: #48bb78;
}

.log-warning {
    color: #ed8936;
}

.log-error {
    color: #f56565;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    text-align: center;
}

.modal-content h3 {
    margin-bottom: 20px;
    color: #2d3748;
}

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 20px;
    background: #e2e8f0;
    border-radius: 10px;
    overflow: hidden;
    margin: 20px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4299e1, #48bb78);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-results {
    max-height: 200px;
    overflow-y: auto;
    text-align: left;
    background: #f7fafc;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    font-family: monospace;
    font-size: 12px;
}

/* 错误页面样式 */
.error-page {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    margin: 50px auto;
}

.error-icon {
    font-size: 4rem;
    color: #f56565;
    margin-bottom: 20px;
}

.error-page h1 {
    font-size: 2rem;
    color: #2d3748;
    margin-bottom: 15px;
}

.error-message {
    font-size: 1.1rem;
    color: #4a5568;
    margin-bottom: 30px;
}

.error-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .form-row {
        flex-direction: column;
        align-items: stretch;
    }

    .form-row label {
        text-align: left;
        min-width: auto;
        margin-bottom: 5px;
    }

    .button-grid {
        grid-template-columns: 1fr;
    }

    .inline-group {
        margin-left: 0;
        margin-top: 10px;
    }

    .modal-content {
        margin: 20px;
        width: calc(100% - 40px);
    }
}