<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>半托找图WEB版 - 商品数据提取工具</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/样式.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部标题 -->
        <header class="header">
            <h1><i class="fas fa-search"></i> 半托找图WEB版</h1>
            <p class="subtitle">商品数据提取和图片下载工具</p>
        </header>

        <!-- 配置区域 -->
        <section class="config-section">
            <h2><i class="fas fa-cog"></i> 配置选项</h2>

            <!-- API配置 -->
            <div class="config-group">
                <h3>API配置</h3>
                <div class="form-row">
                    <label for="api-url">API地址：</label>
                    <input type="text" id="api-url" class="form-input"
                           value="{{ config.API.url if config.API else '' }}"
                           placeholder="请输入API地址">
                </div>
                <div class="form-row">
                    <label for="cookie">Cookie：</label>
                    <input type="text" id="cookie" class="form-input"
                           value="{{ config.API.cookie if config.API else '' }}"
                           placeholder="请输入Cookie信息">
                </div>
            </div>

            <!-- 搜索配置 -->
            <div class="config-group">
                <h3>搜索配置</h3>
                <div class="form-row">
                    <label for="search-path">搜索路径：</label>
                    <input type="text" id="search-path" class="form-input"
                           value="{{ config.SEARCH.base_path if config.SEARCH else '' }}"
                           placeholder="请输入搜索路径">
                </div>
                <div class="form-row">
                    <label for="target-suffix">目标路径后缀：</label>
                    <input type="text" id="target-suffix" class="form-input"
                           value="{{ config.SEARCH.target_suffix if config.SEARCH else '' }}"
                           placeholder="请输入目标路径后缀">
                    <label class="checkbox-label">
                        <input type="checkbox" id="enable-target-suffix"
                               {{ 'checked' if config.SEARCH and config.SEARCH.enable_target_suffix == 'True' else '' }}>
                        启用
                    </label>
                </div>
                <div class="form-row">
                    <label class="checkbox-label">
                        <input type="checkbox" id="ignore-filename-chars"
                               {{ 'checked' if config.SEARCH and config.SEARCH.ignore_filename_chars == 'True' else '' }}>
                        忽略文件名前后字符
                    </label>
                    <span class="inline-group">
                        <label>忽略前</label>
                        <input type="number" id="ignore-prefix-count" class="small-input"
                               value="{{ config.SEARCH.ignore_prefix_count if config.SEARCH else '20' }}" min="0">
                        <label>个字符</label>
                    </span>
                    <span class="inline-group">
                        <label>忽略后</label>
                        <input type="number" id="ignore-suffix-count" class="small-input"
                               value="{{ config.SEARCH.ignore_suffix_count if config.SEARCH else '50' }}" min="0">
                        <label>个字符</label>
                    </span>
                </div>
            </div>

            <!-- 共享文件夹配置 -->
            <div class="config-group">
                <h3>共享文件夹配置</h3>
                <div class="form-row">
                    <label for="shared-folder">共享文件夹：</label>
                    <input type="text" id="shared-folder" class="form-input"
                           value="{{ config.SHARED.folder if config.SHARED else '' }}"
                           placeholder="请输入共享文件夹路径">
                </div>
            </div>

            <!-- 其他选项 -->
            <div class="config-group">
                <h3>其他选项</h3>
                <div class="form-row">
                    <label class="checkbox-label">
                        <input type="checkbox" id="strict-search"
                               {{ 'checked' if config.OPTIONS and config.OPTIONS.strict_search == 'True' else '' }}>
                        严格搜索路径限制（只搜索指定目录）
                    </label>
                </div>
                <p class="help-text">注意：勾选"严格搜索路径限制"可确保只在指定目录中搜索</p>
            </div>

            <div class="form-row">
                <button type="button" id="save-config-btn" class="btn btn-primary">
                    <i class="fas fa-save"></i> 保存配置
                </button>
            </div>
        </section>

        <!-- 商品数据输入区域 -->
        <section class="product-section">
            <h2><i class="fas fa-box"></i> 商品数据输入</h2>
            <div class="form-row">
                <textarea id="product-data" class="form-textarea" rows="6"
                          placeholder="请输入商品数据，每行一个商品，格式：商品名称----商品ID 或 直接输入SKU"></textarea>
            </div>

            <!-- SKU搜索区域 -->
            <div class="sku-search-group">
                <h3>SKU搜索</h3>
                <div class="form-row">
                    <label for="sku-search">SKU：</label>
                    <input type="text" id="sku-search" class="form-input" placeholder="请输入SKU">
                    <button type="button" id="search-sku-btn" class="btn btn-secondary">
                        <i class="fas fa-search"></i> 搜索商品名
                    </button>
                </div>
            </div>
        </section>

        <!-- 操作按钮区域 -->
        <section class="actions-section">
            <h2><i class="fas fa-tools"></i> 操作功能</h2>
            <div class="button-grid">
                <button type="button" id="extract-sku-btn" class="btn btn-success">
                    <i class="fas fa-extract"></i> 提取SKU
                </button>
                <button type="button" id="download-images-btn" class="btn btn-warning">
                    <i class="fas fa-download"></i> 下载图片
                </button>
                <button type="button" id="search-sku-download-btn" class="btn btn-purple">
                    <i class="fas fa-search-plus"></i> SKU搜索下载
                </button>
                <button type="button" id="compare-sku-btn" class="btn btn-danger">
                    <i class="fas fa-balance-scale"></i> SKU对比
                </button>
                <button type="button" id="download-missing-btn" class="btn btn-success">
                    <i class="fas fa-download"></i> 下载缺失SKU
                </button>
                <button type="button" id="rename-files-btn" class="btn btn-info">
                    <i class="fas fa-edit"></i> 重命名文件
                </button>
            </div>
        </section>

        <!-- 操作日志区域 -->
        <section class="console-section">
            <h2><i class="fas fa-terminal"></i> 操作日志</h2>
            <div id="console" class="console"></div>
            <div class="console-controls">
                <button type="button" id="clear-console-btn" class="btn btn-secondary">
                    <i class="fas fa-trash"></i> 清空日志
                </button>
            </div>
        </section>

        <!-- 进度显示区域 -->
        <div id="progress-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <h3><i class="fas fa-spinner fa-spin"></i> 任务进行中</h3>
                <div class="progress-bar">
                    <div id="progress-fill" class="progress-fill"></div>
                </div>
                <p id="progress-text">正在处理...</p>
                <div id="progress-results" class="progress-results"></div>
                <button type="button" id="close-progress-btn" class="btn btn-secondary" style="display: none;">
                    <i class="fas fa-times"></i> 关闭
                </button>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/主脚本.js') }}"></script>
</body>
</html>