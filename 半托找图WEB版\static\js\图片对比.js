// 图片对比页面脚本 - 全屏图片对比版本

// 全局变量
let skuList = [];
let currentIndex = 0;
let currentImageData = null;
let selectedLocalImage = null;
let localImages = [];
let downloadResults = [];

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    initializeMagnifier();
    loadSkuData();
});

// 初始化事件监听器
function initializeEventListeners() {
    // 选择按钮
    document.getElementById('select-api-btn').addEventListener('click', () => selectImage('api'));
    document.getElementById('select-local-btn').addEventListener('click', () => selectImage('local'));

    // 操作按钮
    document.getElementById('skip-btn').addEventListener('click', skipCurrentSku);
    document.getElementById('back-btn').addEventListener('click', () => window.location.href = '/');

    // 确认对话框
    document.getElementById('confirm-yes-btn').addEventListener('click', confirmSelection);
    document.getElementById('confirm-no-btn').addEventListener('click', closeConfirmModal);

    // 完成对话框
    document.getElementById('complete-ok-btn').addEventListener('click', () => window.location.href = '/');
}

// 初始化图片放大镜效果
function initializeMagnifier() {
    const magnifier = document.getElementById('magnifier');

    // 为API图片添加放大镜效果
    const apiImage = document.getElementById('api-image');
    addMagnifierEffect(apiImage, magnifier);

    // 为本地图片添加放大镜效果
    const localImage = document.getElementById('current-local-image');
    addMagnifierEffect(localImage, magnifier);
}

// 添加放大镜效果到图片
function addMagnifierEffect(image, magnifier) {
    image.addEventListener('mousemove', function(e) {
        if (this.style.display === 'none') return;

        const rect = this.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        // 计算放大镜位置
        const magnifierSize = 200;
        const zoom = 2;

        // 设置放大镜位置
        magnifier.style.left = (e.clientX - magnifierSize / 2) + 'px';
        magnifier.style.top = (e.clientY - magnifierSize / 2) + 'px';

        // 计算背景位置
        const bgPosX = -(x * zoom - magnifierSize / 2);
        const bgPosY = -(y * zoom - magnifierSize / 2);

        // 设置放大镜背景
        magnifier.style.backgroundImage = `url(${this.src})`;
        magnifier.style.backgroundPosition = `${bgPosX}px ${bgPosY}px`;
        magnifier.style.display = 'block';
    });

    image.addEventListener('mouseenter', function() {
        if (this.style.display !== 'none') {
            magnifier.style.display = 'block';
        }
    });

    image.addEventListener('mouseleave', function() {
        magnifier.style.display = 'none';
    });
}

// 从URL参数或localStorage加载SKU数据
function loadSkuData() {
    try {
        // 尝试从URL参数获取
        const urlParams = new URLSearchParams(window.location.search);
        const skuParam = urlParams.get('skus');

        if (skuParam) {
            skuList = skuParam.split(',').map(sku => sku.trim()).filter(sku => sku);
        } else {
            // 从localStorage获取
            const storedSkus = localStorage.getItem('compareSkus');
            if (storedSkus) {
                skuList = JSON.parse(storedSkus);
            }
        }

        if (skuList.length === 0) {
            showCompleteModal('没有SKU数据', '请返回主页重新选择SKU');
            return;
        }

        processCurrentSku();

    } catch (error) {
        showCompleteModal('加载失败', '无法加载SKU数据，请返回主页重试');
    }
}

// 处理当前SKU
async function processCurrentSku() {
    if (currentIndex >= skuList.length) {
        showCompleteModal('处理完成', '所有SKU已处理完成');
        return;
    }

    const sku = skuList[currentIndex];
    updateProgress();

    // 更新界面显示
    document.getElementById('current-sku').textContent = `SKU: ${sku}`;

    // 重置界面状态
    resetImagePanels();

    try {
        // 首先搜索SKU获取商品信息
        const skuData = await searchSkuInfo(sku);

        if (skuData.success) {
            currentImageData = skuData;

            // 并行加载API图片和搜索本地图片
            await Promise.all([
                loadApiImage(skuData.thumb_url),
                searchLocalImages(sku, skuData.product_name, skuData.thumb_url)
            ]);
        } else {
            showApiImageError();
            showLocalImagesEmpty();
        }

    } catch (error) {
        showApiImageError();
        showLocalImagesEmpty();
    }
}

// 搜索SKU信息
async function searchSkuInfo(sku) {
    const config = await getConfig();

    const response = await fetch('/api/search_sku', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            sku: sku,
            cookie: config.API.cookie
        })
    });

    return await response.json();
}

// 获取配置信息
async function getConfig() {
    const response = await fetch('/api/config');
    return await response.json();
}

// 加载API图片
async function loadApiImage(thumbUrl) {
    const apiImage = document.getElementById('api-image');
    const loadingDiv = document.getElementById('api-image-loading');
    const errorDiv = document.getElementById('api-image-error');
    const selectBtn = document.getElementById('select-api-btn');

    if (!thumbUrl) {
        showApiImageError();
        return;
    }

    // 显示加载状态
    loadingDiv.style.display = 'flex';
    apiImage.style.display = 'none';
    errorDiv.style.display = 'none';
    selectBtn.disabled = true;

    try {
        // 创建新的图片对象来测试加载
        const testImage = new Image();

        testImage.onload = function() {
            apiImage.src = thumbUrl;
            apiImage.style.display = 'block';
            loadingDiv.style.display = 'none';
            selectBtn.disabled = false;
        };

        testImage.onerror = function() {
            showApiImageError();
        };

        testImage.src = thumbUrl;

    } catch (error) {
        showApiImageError();
    }
}

// 显示API图片错误
function showApiImageError() {
    document.getElementById('api-image-loading').style.display = 'none';
    document.getElementById('api-image').style.display = 'none';
    document.getElementById('api-image-error').style.display = 'flex';
    document.getElementById('select-api-btn').disabled = true;
}

// 搜索本地图片
async function searchLocalImages(sku, productName, thumbUrl) {
    const loadingDiv = document.getElementById('local-image-loading');
    const localImage = document.getElementById('current-local-image');
    const emptyDiv = document.getElementById('local-images-empty');
    const selectBtn = document.getElementById('select-local-btn');
    const selector = document.getElementById('local-selector');

    // 显示加载状态
    loadingDiv.style.display = 'flex';
    localImage.style.display = 'none';
    emptyDiv.style.display = 'none';
    selectBtn.disabled = true;
    selector.style.display = 'none';

    try {
        const config = await getConfig();

        const response = await fetch('/api/search_images', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sku: sku,
                product_name: productName,
                thumb_url: thumbUrl,
                config: config
            })
        });

        const result = await response.json();

        if (result.success && result.local_images.length > 0) {
            localImages = result.local_images;
            displayLocalImages(result.local_images);
        } else {
            showLocalImagesEmpty();
        }

    } catch (error) {
        showLocalImagesEmpty();
    }
}

// 显示本地图片
function displayLocalImages(images) {
    const loadingDiv = document.getElementById('local-image-loading');
    const localImage = document.getElementById('current-local-image');
    const selectBtn = document.getElementById('select-local-btn');
    const selector = document.getElementById('local-selector');
    const selectorContainer = selector.querySelector('.selector-container');

    // 隐藏加载状态
    loadingDiv.style.display = 'none';

    if (images.length === 1) {
        // 只有一张图片，直接显示
        showSingleLocalImage(images[0]);
        selector.style.display = 'none';
    } else {
        // 多张图片，显示第一张并创建选择器
        showSingleLocalImage(images[0]);
        createImageSelector(images, selectorContainer);
        selector.style.display = 'block';

        // 调整底部按钮位置
        const bottomActions = document.querySelector('.bottom-actions');
        bottomActions.classList.add('with-selector');
    }

    selectBtn.disabled = false;
}

// 显示单张本地图片
function showSingleLocalImage(image) {
    const localImage = document.getElementById('current-local-image');
    selectedLocalImage = image;

    localImage.src = image.url;
    localImage.style.display = 'block';
}

// 创建图片选择器
function createImageSelector(images, container) {
    container.innerHTML = '';

    images.forEach((image, index) => {
        const thumbnail = document.createElement('div');
        thumbnail.className = 'thumbnail-item';
        if (index === 0) thumbnail.classList.add('active');

        const img = document.createElement('img');
        img.src = image.url;
        img.alt = image.name;
        img.loading = 'lazy';

        thumbnail.appendChild(img);

        // 添加点击事件
        thumbnail.addEventListener('click', () => {
            // 移除其他缩略图的激活状态
            container.querySelectorAll('.thumbnail-item').forEach(item => {
                item.classList.remove('active');
            });

            // 激活当前缩略图
            thumbnail.classList.add('active');

            // 显示选中的图片
            showSingleLocalImage(image);
        });

        container.appendChild(thumbnail);

        // 添加动画延迟
        setTimeout(() => {
            thumbnail.style.animationDelay = `${index * 0.1}s`;
        }, 0);
    });
}

// 显示本地图片为空
function showLocalImagesEmpty() {
    document.getElementById('local-image-loading').style.display = 'none';
    document.getElementById('current-local-image').style.display = 'none';
    document.getElementById('local-images-empty').style.display = 'flex';
    document.getElementById('select-local-btn').disabled = true;
    document.getElementById('local-selector').style.display = 'none';
}

// 选择图片（API或本地）
function selectImage(type) {
    if (type === 'api') {
        if (!currentImageData || !currentImageData.thumb_url) {
            return;
        }
        showConfirmModal('API图片', `确定要下载API商品图吗？`);
    } else if (type === 'local' && selectedLocalImage) {
        showConfirmModal('本地图片', `确定要下载本地图片吗？`);
    }
}

// 显示确认对话框
function showConfirmModal(imageType, message) {
    document.getElementById('confirm-message').textContent = message;
    document.getElementById('confirm-modal').style.display = 'flex';

    // 存储选择类型
    document.getElementById('confirm-modal').dataset.imageType = imageType === 'API图片' ? 'api' : 'local';
}

// 关闭确认对话框
function closeConfirmModal() {
    document.getElementById('confirm-modal').style.display = 'none';
}

// 确认选择
async function confirmSelection() {
    const modal = document.getElementById('confirm-modal');
    const imageType = modal.dataset.imageType;

    closeConfirmModal();

    try {
        let imageUrl;
        if (imageType === 'api') {
            imageUrl = currentImageData.thumb_url;
        } else {
            imageUrl = selectedLocalImage.url;
        }

        logMessage(`开始下载${imageType === 'api' ? 'API' : '本地'}图片...`, 'info');

        const response = await fetch('/api/download_selected_image', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sku: skuList[currentIndex],
                image_type: imageType,
                image_url: imageUrl
            })
        });

        const result = await response.json();

        if (result.success) {
            logMessage(`图片下载成功: ${result.file_path}`, 'success');
            downloadResults.push({
                sku: skuList[currentIndex],
                type: imageType,
                success: true,
                file_path: result.file_path
            });
        } else {
            logMessage(`图片下载失败: ${result.message}`, 'error');
            downloadResults.push({
                sku: skuList[currentIndex],
                type: imageType,
                success: false,
                error: result.message
            });
        }

        // 处理下一个SKU
        nextSku();

    } catch (error) {
        logMessage(`下载图片时出错: ${error.message}`, 'error');
        downloadResults.push({
            sku: skuList[currentIndex],
            type: imageType,
            success: false,
            error: error.message
        });
        nextSku();
    }
}

// 跳过当前SKU
function skipCurrentSku() {
    downloadResults.push({
        sku: skuList[currentIndex],
        type: 'skipped',
        success: false,
        error: '用户跳过'
    });
    nextSku();
}

// 处理下一个SKU
function nextSku() {
    currentIndex++;
    selectedLocalImage = null;
    currentImageData = null;
    localImages = [];

    // 隐藏本地选择器
    const selector = document.getElementById('local-selector');
    selector.style.display = 'none';

    // 重置底部按钮位置
    const bottomActions = document.querySelector('.bottom-actions');
    bottomActions.classList.remove('with-selector');

    // 延迟处理下一个SKU
    setTimeout(() => {
        processCurrentSku();
    }, 500);
}

// 重置图片面板
function resetImagePanels() {
    // 重置API图片面板
    document.getElementById('api-image-loading').style.display = 'flex';
    document.getElementById('api-image').style.display = 'none';
    document.getElementById('api-image-error').style.display = 'none';
    document.getElementById('select-api-btn').disabled = true;

    // 重置本地图片面板
    document.getElementById('local-image-loading').style.display = 'flex';
    document.getElementById('current-local-image').style.display = 'none';
    document.getElementById('local-images-empty').style.display = 'none';
    document.getElementById('select-local-btn').disabled = true;

    // 隐藏本地选择器
    document.getElementById('local-selector').style.display = 'none';

    // 清除选择状态
    selectedLocalImage = null;
}

// 更新进度显示
function updateProgress() {
    const progressText = document.getElementById('progress-text');
    progressText.textContent = `进度: ${currentIndex + 1}/${skuList.length}`;
}

// 显示完成对话框
function showCompleteModal(title, message) {
    document.getElementById('complete-message').textContent = message;

    // 生成处理结果摘要
    const summary = generateSummary();
    document.getElementById('complete-summary').innerHTML = summary;

    document.getElementById('complete-modal').style.display = 'flex';
}

// 生成处理结果摘要
function generateSummary() {
    if (downloadResults.length === 0) {
        return '<p>没有处理任何SKU</p>';
    }

    const successful = downloadResults.filter(r => r.success).length;
    const failed = downloadResults.filter(r => !r.success).length;
    const skipped = downloadResults.filter(r => r.type === 'skipped').length;

    let summary = `
        <div style="margin-bottom: 15px;">
            <strong>处理结果统计：</strong><br>
            总计: ${downloadResults.length} 个SKU<br>
            成功: ${successful} 个<br>
            失败: ${failed} 个<br>
            跳过: ${skipped} 个
        </div>
        <div style="max-height: 150px; overflow-y: auto;">
            <strong>详细结果：</strong><br>
    `;

    downloadResults.forEach(result => {
        const status = result.success ? '✅' : (result.type === 'skipped' ? '⏭️' : '❌');
        const info = result.success ? result.file_path : result.error;
        summary += `${status} ${result.sku}: ${info}<br>`;
    });

    summary += '</div>';
    return summary;
}

// 显示确认对话框
function showConfirmModal(imageType, message) {
    document.getElementById('confirm-message').textContent = message;
    document.getElementById('confirm-modal').style.display = 'flex';

    // 存储选择类型
    document.getElementById('confirm-modal').dataset.imageType = imageType === 'API图片' ? 'api' : 'local';
}

// 关闭确认对话框
function closeConfirmModal() {
    document.getElementById('confirm-modal').style.display = 'none';
}

// 确认选择
async function confirmSelection() {
    const modal = document.getElementById('confirm-modal');
    const imageType = modal.dataset.imageType;

    closeConfirmModal();

    try {
        let imageUrl;
        if (imageType === 'api') {
            imageUrl = currentImageData.thumb_url;
        } else {
            imageUrl = selectedLocalImage.url;
        }

        const response = await fetch('/api/download_selected_image', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                sku: skuList[currentIndex],
                image_type: imageType,
                image_url: imageUrl
            })
        });

        const result = await response.json();

        downloadResults.push({
            sku: skuList[currentIndex],
            type: imageType,
            success: result.success,
            file_path: result.success ? result.file_path : null,
            error: result.success ? null : result.message
        });

        // 处理下一个SKU
        nextSku();

    } catch (error) {
        downloadResults.push({
            sku: skuList[currentIndex],
            type: imageType,
            success: false,
            error: error.message
        });
        nextSku();
    }
}

// 显示完成对话框
function showCompleteModal(title, message) {
    document.getElementById('complete-message').textContent = message;

    // 生成处理结果摘要
    const summary = generateSummary();
    document.getElementById('complete-summary').innerHTML = summary;

    document.getElementById('complete-modal').style.display = 'flex';
}

// 生成处理结果摘要
function generateSummary() {
    if (downloadResults.length === 0) {
        return '<p>没有处理任何SKU</p>';
    }

    const successful = downloadResults.filter(r => r.success).length;
    const failed = downloadResults.filter(r => !r.success).length;
    const skipped = downloadResults.filter(r => r.type === 'skipped').length;

    let summary = `
        <div style="margin-bottom: 15px;">
            <strong>处理结果统计：</strong><br>
            总计: ${downloadResults.length} 个SKU<br>
            成功: ${successful} 个<br>
            失败: ${failed} 个<br>
            跳过: ${skipped} 个
        </div>
        <div style="max-height: 150px; overflow-y: auto;">
            <strong>详细结果：</strong><br>
    `;

    downloadResults.forEach(result => {
        const status = result.success ? '✅' : (result.type === 'skipped' ? '⏭️' : '❌');
        const info = result.success ? result.file_path : result.error;
        summary += `${status} ${result.sku}: ${info}<br>`;
    });

    summary += '</div>';
    return summary;
}