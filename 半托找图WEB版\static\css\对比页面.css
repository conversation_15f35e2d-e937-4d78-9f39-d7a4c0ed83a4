/* 图片对比页面专用样式 - 全屏图片对比 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "微软雅黑", "Microsoft YaHei", Arial, sans-serif;
    background: #ffffff;
    color: #333333;
    overflow-x: hidden;
    height: 100vh;
}

/* 顶部信息栏 */
.top-info {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    z-index: 1000;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.top-info span {
    font-size: 16px;
    font-weight: 500;
}

.back-btn {
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.2);
    color: #333;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.back-btn:hover {
    background: rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
}

/* 主要对比区域 */
.main-compare {
    display: grid;
    grid-template-columns: 1fr 1fr;
    height: 100vh;
    padding-top: 60px;
}

.image-side {
    position: relative;
    display: flex;
    flex-direction: column;
    background: #ffffff;
}

.left-side {
    border-right: 2px solid #e2e8f0;
}

.right-side {
    border-left: 2px solid #e2e8f0;
}

/* 图片显示区域 */
.image-display {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    min-height: calc(100vh - 180px);
    max-height: calc(100vh - 180px);
}

.main-image {
    width: calc(100% - 40px);
    height: calc(100% - 40px);
    max-width: calc(100% - 40px);
    max-height: calc(100% - 40px);
    object-fit: contain;
    cursor: crosshair;
    transition: all 0.3s ease;
    border-radius: 8px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    margin: 20px;
}

.main-image:hover {
    transform: scale(1.02);
    box-shadow: 0 15px 60px rgba(0, 0, 0, 0.2);
}

/* 加载和错误状态 */
.loading,
.error {
    text-align: center;
    color: #666;
    padding: 40px;
}

.loading i,
.error i {
    font-size: 4rem;
    margin-bottom: 20px;
    display: block;
}

.loading i {
    color: #4299e1;
    animation: spin 1s linear infinite;
}

.error i {
    color: #f56565;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.loading p,
.error p {
    font-size: 18px;
    margin-top: 10px;
}

/* 图片操作按钮 */
.image-actions {
    padding: 20px;
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.select-btn {
    padding: 15px 30px;
    font-size: 16px;
    font-weight: bold;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
}

.api-btn {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.local-btn {
    background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
    color: white;
}

.select-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.select-btn:disabled {
    background: #e2e8f0;
    color: #a0aec0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 本地图片选择器 */
.local-selector {
    position: fixed;
    bottom: 0;
    left: 50%;
    right: 0;
    height: 120px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 999;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.selector-container {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 10px 20px;
    gap: 15px;
    overflow-x: auto;
}

.selector-container::-webkit-scrollbar {
    height: 6px;
}

.selector-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
}

.selector-container::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
}

.thumbnail-item {
    flex-shrink: 0;
    width: 100px;
    height: 100px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.3s ease;
    position: relative;
}

.thumbnail-item:hover {
    border-color: #9f7aea;
    transform: scale(1.05);
}

.thumbnail-item.active {
    border-color: #48bb78;
    box-shadow: 0 0 20px rgba(72, 187, 120, 0.5);
}

.thumbnail-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 底部操作按钮 */
.bottom-actions {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
}

.action-btn {
    padding: 12px 24px;
    font-size: 14px;
    font-weight: bold;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(0, 0, 0, 0.1);
    color: #333;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.action-btn:hover {
    background: rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
}

/* 图片放大镜效果 */
.magnifier {
    position: absolute;
    width: 200px;
    height: 200px;
    border: 3px solid #fff;
    border-radius: 50%;
    background-repeat: no-repeat;
    background-size: 400px 400px;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.8);
    pointer-events: none;
    z-index: 1001;
    backdrop-filter: blur(0px);
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: #ffffff;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    max-width: 400px;
    width: 90%;
    text-align: center;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.modal-content h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 20px;
}

.modal-content p {
    margin-bottom: 25px;
    color: #666;
    line-height: 1.5;
}

.modal-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.confirm-btn,
.cancel-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
}

.confirm-btn {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.cancel-btn {
    background: rgba(0, 0, 0, 0.1);
    color: #333;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.confirm-btn:hover,
.cancel-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* 完成摘要 */
.complete-summary {
    background: rgba(0, 0, 0, 0.05);
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    text-align: left;
    font-family: monospace;
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-compare {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr 1fr;
    }

    .left-side {
        border-right: none;
        border-bottom: 2px solid #e2e8f0;
    }

    .right-side {
        border-left: none;
        border-top: 2px solid #e2e8f0;
    }

    .image-display {
        min-height: calc(50vh - 90px);
        max-height: calc(50vh - 90px);
    }

    .local-selector {
        left: 0;
        height: 100px;
    }

    .thumbnail-item {
        width: 80px;
        height: 80px;
    }

    .top-info {
        padding: 0 15px;
        font-size: 14px;
    }

    .select-btn {
        padding: 12px 20px;
        font-size: 14px;
        min-width: 150px;
    }

    .magnifier {
        width: 150px;
        height: 150px;
        background-size: 300px 300px;
    }
}

/* 当本地选择器显示时，调整底部按钮位置 */
.bottom-actions.with-selector {
    bottom: 140px;
}

/* 图片加载动画 */
.main-image {
    opacity: 0;
    animation: fadeIn 0.5s ease-in-out forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 缩略图加载动画 */
.thumbnail-item {
    opacity: 0;
    animation: slideIn 0.3s ease-out forwards;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 面板操作按钮 */
.panel-actions {
    text-align: center;
}

.panel-actions .btn {
    min-width: 150px;
}

/* 操作按钮区域 */
.actions-section {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

/* 模态框操作按钮 */
.modal-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

/* 完成摘要 */
.complete-summary {
    background: #f7fafc;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    text-align: left;
    font-family: monospace;
    font-size: 0.9rem;
    max-height: 200px;
    overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .compare-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .panel-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .image-info {
        align-items: flex-start;
    }

    .image-container {
        min-height: 300px;
    }

    .local-images-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 10px;
    }

    .local-image-item img {
        height: 100px;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .modal-actions {
        flex-direction: column;
    }
}